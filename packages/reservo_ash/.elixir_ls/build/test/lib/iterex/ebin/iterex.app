{application,iterex,
             [{modules,['Elixir.Collectable.Iter','Elixir.Enumerable.Iter',
                        'Elixir.Iter','Elixir.Iter.Impl',
                        'Elixir.Iter.IntoIterable',
                        'Elixir.Iter.IntoIterable.Date.Range',
                        'Elixir.Iter.IntoIterable.File.Stream',
                        'Elixir.Iter.IntoIterable.IO.Stream',
                        'Elixir.Iter.IntoIterable.Iter',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Appender',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.ByChunker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Concatenator',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Cycler',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Deduper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Empty',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Enumerable',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.EveryChunker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.EveryDropper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.EveryMapper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Filterer',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.FlatMapper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Flattener',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.HeadDropper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.HeadTaker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Intersperser',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Map',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Mapper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Peeker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Prepender',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Resource',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Stepper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.TailDropper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.TailTaker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Uniquer',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.WhileChunker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.WhileDropper',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.WhileTaker',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.WithIndexer',
                        'Elixir.Iter.IntoIterable.Iter.Iterable.Zipper',
                        'Elixir.Iter.IntoIterable.List',
                        'Elixir.Iter.IntoIterable.Map',
                        'Elixir.Iter.IntoIterable.MapSet',
                        'Elixir.Iter.IntoIterable.Range',
                        'Elixir.Iter.Iterable',
                        'Elixir.Iter.Iterable.Appender',
                        'Elixir.Iter.Iterable.ByChunker',
                        'Elixir.Iter.Iterable.Concatenator',
                        'Elixir.Iter.Iterable.Cycler',
                        'Elixir.Iter.Iterable.Date.Range',
                        'Elixir.Iter.Iterable.Deduper',
                        'Elixir.Iter.Iterable.Empty',
                        'Elixir.Iter.Iterable.Enumerable',
                        'Elixir.Iter.Iterable.EveryChunker',
                        'Elixir.Iter.Iterable.EveryDropper',
                        'Elixir.Iter.Iterable.EveryMapper',
                        'Elixir.Iter.Iterable.Filterer',
                        'Elixir.Iter.Iterable.FlatMapper',
                        'Elixir.Iter.Iterable.Flattener',
                        'Elixir.Iter.Iterable.HeadDropper',
                        'Elixir.Iter.Iterable.HeadTaker',
                        'Elixir.Iter.Iterable.Intersperser',
                        'Elixir.Iter.Iterable.Iter.Iterable.Appender',
                        'Elixir.Iter.Iterable.Iter.Iterable.ByChunker',
                        'Elixir.Iter.Iterable.Iter.Iterable.Concatenator',
                        'Elixir.Iter.Iterable.Iter.Iterable.Cycler',
                        'Elixir.Iter.Iterable.Iter.Iterable.Deduper',
                        'Elixir.Iter.Iterable.Iter.Iterable.Empty',
                        'Elixir.Iter.Iterable.Iter.Iterable.Enumerable',
                        'Elixir.Iter.Iterable.Iter.Iterable.EveryChunker',
                        'Elixir.Iter.Iterable.Iter.Iterable.EveryDropper',
                        'Elixir.Iter.Iterable.Iter.Iterable.EveryMapper',
                        'Elixir.Iter.Iterable.Iter.Iterable.Filterer',
                        'Elixir.Iter.Iterable.Iter.Iterable.FlatMapper',
                        'Elixir.Iter.Iterable.Iter.Iterable.Flattener',
                        'Elixir.Iter.Iterable.Iter.Iterable.HeadDropper',
                        'Elixir.Iter.Iterable.Iter.Iterable.HeadTaker',
                        'Elixir.Iter.Iterable.Iter.Iterable.Intersperser',
                        'Elixir.Iter.Iterable.Iter.Iterable.Map',
                        'Elixir.Iter.Iterable.Iter.Iterable.Mapper',
                        'Elixir.Iter.Iterable.Iter.Iterable.Peeker',
                        'Elixir.Iter.Iterable.Iter.Iterable.Prepender',
                        'Elixir.Iter.Iterable.Iter.Iterable.Resource',
                        'Elixir.Iter.Iterable.Iter.Iterable.Stepper',
                        'Elixir.Iter.Iterable.Iter.Iterable.TailDropper',
                        'Elixir.Iter.Iterable.Iter.Iterable.TailTaker',
                        'Elixir.Iter.Iterable.Iter.Iterable.Uniquer',
                        'Elixir.Iter.Iterable.Iter.Iterable.WhileChunker',
                        'Elixir.Iter.Iterable.Iter.Iterable.WhileDropper',
                        'Elixir.Iter.Iterable.Iter.Iterable.WhileTaker',
                        'Elixir.Iter.Iterable.Iter.Iterable.WithIndexer',
                        'Elixir.Iter.Iterable.Iter.Iterable.Zipper',
                        'Elixir.Iter.Iterable.List',
                        'Elixir.Iter.Iterable.Map',
                        'Elixir.Iter.Iterable.MapSet',
                        'Elixir.Iter.Iterable.Mapper',
                        'Elixir.Iter.Iterable.Peeker',
                        'Elixir.Iter.Iterable.Prepender',
                        'Elixir.Iter.Iterable.Range',
                        'Elixir.Iter.Iterable.Resource',
                        'Elixir.Iter.Iterable.Stepper',
                        'Elixir.Iter.Iterable.TailDropper',
                        'Elixir.Iter.Iterable.TailTaker',
                        'Elixir.Iter.Iterable.Uniquer',
                        'Elixir.Iter.Iterable.WhileChunker',
                        'Elixir.Iter.Iterable.WhileDropper',
                        'Elixir.Iter.Iterable.WhileTaker',
                        'Elixir.Iter.Iterable.WithIndexer',
                        'Elixir.Iter.Iterable.Zipper']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"Lazy, external iterators for Elixir.\n"},
              {registered,[]},
              {vsn,"0.1.2"}]}.
