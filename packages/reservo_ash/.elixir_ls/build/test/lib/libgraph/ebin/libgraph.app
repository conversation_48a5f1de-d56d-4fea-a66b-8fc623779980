{application,libgraph,
             [{modules,['Elixir.Graph','Elixir.Graph.Directed',
                        'Elixir.Graph.Edge',
                        'Elixir.Graph.EdgeSpecificationError',
                        'Elixir.Graph.Pathfinding','Elixir.Graph.Reducer',
                        'Elixir.Graph.Reducers.Bfs',
                        'Elixir.Graph.Reducers.Dfs','Elixir.Graph.Serializer',
                        'Elixir.Graph.Serializers.DOT',
                        'Elixir.Graph.Serializers.Edgelist',
                        'Elixir.Graph.Utils','Elixir.Inspect.Graph',
                        'Elixir.Inspect.PriorityQueue',
                        'Elixir.Pathfindings.BellmanFord',
                        'Elixir.PriorityQueue']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"A high-performance graph datastructure library for Elixir projects"},
              {registered,[]},
              {vsn,"0.16.0"}]}.
