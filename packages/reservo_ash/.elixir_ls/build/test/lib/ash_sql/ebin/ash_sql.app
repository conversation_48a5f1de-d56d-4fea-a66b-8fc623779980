{application,ash_sql,
             [{modules,['Elixir.AshSql','Elixir.AshSql.Aggregate',
                        'Elixir.AshSql.AggregateQuery',
                        'Elixir.AshSql.Atomics','Elixir.AshSql.Bindings',
                        'Elixir.AshSql.Calculation','Elixir.AshSql.Distinct',
                        'Elixir.AshSql.Expr','Elixir.AshSql.Expr.ExprInfo',
                        'Elixir.AshSql.Filter','Elixir.AshSql.Implementation',
                        'Elixir.AshSql.Join','Elixir.AshSql.Query',
                        'Elixir.AshSql.Sort']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,ash,ecto_sql,ecto]},
              {description,"Shared utilities for ecto-based sql data layers.\n"},
              {registered,[]},
              {vsn,"0.2.83"}]}.
