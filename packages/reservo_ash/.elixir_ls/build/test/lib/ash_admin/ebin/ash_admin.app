{application,ash_admin,
             [{modules,['Elixir.AshAdmin','Elixir.AshAdmin.ActorPlug',
                        'Elixir.AshAdmin.ActorPlug.Plug',
                        'Elixir.AshAdmin.Components.Resource',
                        'Elixir.AshAdmin.Components.Resource.DataTable',
                        'Elixir.AshAdmin.Components.Resource.Form',
                        'Elixir.AshAdmin.Components.Resource.GenericAction',
                        'Elixir.AshAdmin.Components.Resource.Helpers.FormatHelper',
                        'Elixir.AshAdmin.Components.Resource.Info',
                        'Elixir.AshAdmin.Components.Resource.MetadataTable',
                        'Elixir.AshAdmin.Components.Resource.Nav',
                        'Elixir.AshAdmin.Components.Resource.RelationshipField',
                        'Elixir.AshAdmin.Components.Resource.SelectTable',
                        'Elixir.AshAdmin.Components.Resource.SensitiveAttribute',
                        'Elixir.AshAdmin.Components.Resource.Show',
                        'Elixir.AshAdmin.Components.Resource.Table',
                        'Elixir.AshAdmin.Components.TopNav',
                        'Elixir.AshAdmin.Components.TopNav.ActorSelect',
                        'Elixir.AshAdmin.Components.TopNav.DrawerDropdown',
                        'Elixir.AshAdmin.Components.TopNav.Dropdown',
                        'Elixir.AshAdmin.Components.TopNav.DropdownHelper',
                        'Elixir.AshAdmin.Components.TopNav.TenantForm',
                        'Elixir.AshAdmin.CoreComponents',
                        'Elixir.AshAdmin.Domain',
                        'Elixir.AshAdmin.Domain.Admin.Options',
                        'Elixir.AshAdmin.Errors.NotFound',
                        'Elixir.AshAdmin.Gettext','Elixir.AshAdmin.Helpers',
                        'Elixir.AshAdmin.Layouts','Elixir.AshAdmin.PageLive',
                        'Elixir.AshAdmin.PageNotFound',
                        'Elixir.AshAdmin.Resource',
                        'Elixir.AshAdmin.Resource.Admin.Form.Field',
                        'Elixir.AshAdmin.Resource.Admin.Form.Field.Options',
                        'Elixir.AshAdmin.Resource.Admin.Options',
                        'Elixir.AshAdmin.Resource.Field',
                        'Elixir.AshAdmin.Resource.Form',
                        'Elixir.AshAdmin.Resource.Transformers.AddPositionSortCalculation',
                        'Elixir.AshAdmin.Resource.Transformers.ValidateTableColumns',
                        'Elixir.AshAdmin.Resource.Verifiers.VerifyFileArgumentsExist',
                        'Elixir.AshAdmin.Router',
                        'Elixir.AshAdmin.SessionPlug',
                        'Elixir.AshAdmin.ShowResourcesTransformer',
                        'Elixir.AshAdmin.Web',
                        'Elixir.Mix.Tasks.AshAdmin.Install',
                        'Elixir.Mix.Tasks.AshAdmin.Install.Docs',
                        'Elixir.Plug.Exception.AshAdmin.Errors.NotFound']},
              {compile_env,[{ash_admin,['Elixir.AshAdmin.Gettext'],error},
                            {ash_admin,[actor_plug],error}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,ash,ash_phoenix,
                             phoenix_view,phoenix,phoenix_live_view,
                             phoenix_html,jason,gettext]},
              {description,"A super-admin UI for Ash Framework, built with Phoenix LiveView.\n"},
              {registered,[]},
              {vsn,"0.13.10"}]}.
