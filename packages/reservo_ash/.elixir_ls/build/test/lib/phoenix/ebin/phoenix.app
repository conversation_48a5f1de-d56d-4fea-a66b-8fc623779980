{application,phoenix,
             [{modules,['Elixir.Mix.Phoenix','Elixir.Mix.Phoenix.Context',
                        'Elixir.Mix.Phoenix.Schema',
                        'Elixir.Mix.Tasks.Compile.Phoenix',
                        'Elixir.Mix.Tasks.Phx','Elixir.Mix.Tasks.Phx.Digest',
                        'Elixir.Mix.Tasks.Phx.Digest.Clean',
                        'Elixir.Mix.Tasks.Phx.Gen',
                        'Elixir.Mix.Tasks.Phx.Gen.Auth',
                        'Elixir.Mix.Tasks.Phx.Gen.Auth.HashingLibrary',
                        'Elixir.Mix.Tasks.Phx.Gen.Auth.Injector',
                        'Elixir.Mix.Tasks.Phx.Gen.Auth.Migration',
                        'Elixir.Mix.Tasks.Phx.Gen.Cert',
                        'Elixir.Mix.Tasks.Phx.Gen.Channel',
                        'Elixir.Mix.Tasks.Phx.Gen.Context',
                        'Elixir.Mix.Tasks.Phx.Gen.Embedded',
                        'Elixir.Mix.Tasks.Phx.Gen.Html',
                        'Elixir.Mix.Tasks.Phx.Gen.Json',
                        'Elixir.Mix.Tasks.Phx.Gen.Live',
                        'Elixir.Mix.Tasks.Phx.Gen.Notifier',
                        'Elixir.Mix.Tasks.Phx.Gen.Presence',
                        'Elixir.Mix.Tasks.Phx.Gen.Release',
                        'Elixir.Mix.Tasks.Phx.Gen.Schema',
                        'Elixir.Mix.Tasks.Phx.Gen.Secret',
                        'Elixir.Mix.Tasks.Phx.Gen.Socket',
                        'Elixir.Mix.Tasks.Phx.Routes',
                        'Elixir.Mix.Tasks.Phx.Server','Elixir.Phoenix',
                        'Elixir.Phoenix.ActionClauseError',
                        'Elixir.Phoenix.Channel',
                        'Elixir.Phoenix.Channel.Server',
                        'Elixir.Phoenix.ChannelTest',
                        'Elixir.Phoenix.ChannelTest.NoopSerializer',
                        'Elixir.Phoenix.CodeReloader',
                        'Elixir.Phoenix.CodeReloader.Proxy',
                        'Elixir.Phoenix.CodeReloader.Server',
                        'Elixir.Phoenix.Config','Elixir.Phoenix.ConnTest',
                        'Elixir.Phoenix.Controller',
                        'Elixir.Phoenix.Controller.Pipeline',
                        'Elixir.Phoenix.Digester',
                        'Elixir.Phoenix.Digester.Compressor',
                        'Elixir.Phoenix.Digester.Gzip',
                        'Elixir.Phoenix.Endpoint',
                        'Elixir.Phoenix.Endpoint.Cowboy2Adapter',
                        'Elixir.Phoenix.Endpoint.RenderErrors',
                        'Elixir.Phoenix.Endpoint.Supervisor',
                        'Elixir.Phoenix.Endpoint.SyncCodeReloadPlug',
                        'Elixir.Phoenix.Endpoint.Watcher',
                        'Elixir.Phoenix.Flash','Elixir.Phoenix.Logger',
                        'Elixir.Phoenix.MissingParamError',
                        'Elixir.Phoenix.Naming',
                        'Elixir.Phoenix.NotAcceptableError',
                        'Elixir.Phoenix.Param','Elixir.Phoenix.Param.Any',
                        'Elixir.Phoenix.Param.Atom',
                        'Elixir.Phoenix.Param.BitString',
                        'Elixir.Phoenix.Param.Float',
                        'Elixir.Phoenix.Param.Integer',
                        'Elixir.Phoenix.Param.Map','Elixir.Phoenix.Presence',
                        'Elixir.Phoenix.Presence.Tracker',
                        'Elixir.Phoenix.Router',
                        'Elixir.Phoenix.Router.ConsoleFormatter',
                        'Elixir.Phoenix.Router.Helpers',
                        'Elixir.Phoenix.Router.MalformedURIError',
                        'Elixir.Phoenix.Router.NoRouteError',
                        'Elixir.Phoenix.Router.Resource',
                        'Elixir.Phoenix.Router.Route',
                        'Elixir.Phoenix.Router.Scope','Elixir.Phoenix.Socket',
                        'Elixir.Phoenix.Socket.Broadcast',
                        'Elixir.Phoenix.Socket.InvalidMessageError',
                        'Elixir.Phoenix.Socket.Message',
                        'Elixir.Phoenix.Socket.PoolDrainer',
                        'Elixir.Phoenix.Socket.PoolSupervisor',
                        'Elixir.Phoenix.Socket.Reply',
                        'Elixir.Phoenix.Socket.Serializer',
                        'Elixir.Phoenix.Socket.Transport',
                        'Elixir.Phoenix.Socket.V1.JSONSerializer',
                        'Elixir.Phoenix.Socket.V2.JSONSerializer',
                        'Elixir.Phoenix.Token',
                        'Elixir.Phoenix.Transports.LongPoll',
                        'Elixir.Phoenix.Transports.LongPoll.Server',
                        'Elixir.Phoenix.Transports.WebSocket',
                        'Elixir.Phoenix.VerifiedRoutes',
                        'Elixir.Plug.Exception.Phoenix.ActionClauseError']},
              {optional_applications,[phoenix_view,plug_cowboy,jason]},
              {applications,[kernel,stdlib,elixir,logger,eex,crypto,
                             public_key,plug,plug_crypto,telemetry,
                             phoenix_pubsub,phoenix_template,websock_adapter,
                             phoenix_view,castore,plug_cowboy,jason]},
              {description,"Peace of mind from prototype to production"},
              {registered,[]},
              {vsn,"1.7.21"},
              {mod,{'Elixir.Phoenix',[]}},
              {env,[{logger,true},
                    {stacktrace_depth,nil},
                    {filter_parameters,[<<"password">>]},
                    {serve_endpoints,false},
                    {gzippable_exts,[<<".js">>,<<".map">>,<<".css">>,
                                     <<".txt">>,<<".text">>,<<".html">>,
                                     <<".json">>,<<".svg">>,<<".eot">>,
                                     <<".ttf">>]},
                    {static_compressors,['Elixir.Phoenix.Digester.Gzip']}]}]}.
