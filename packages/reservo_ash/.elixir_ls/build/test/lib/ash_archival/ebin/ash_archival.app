{application,ash_archival,
             [{modules,['Elixir.AshArchival',
                        'Elixir.AshArchival.ArchiveRelatedArguments',
                        'Elixir.AshArchival.ArchiveRelatedArguments.Function',
                        'Elixir.AshArchival.Resource',
                        'Elixir.AshArchival.Resource.Archive.Options',
                        'Elixir.AshArchival.Resource.Changes.ArchiveRelated',
                        'Elixir.AshArchival.Resource.Info',
                        'Elixir.AshArchival.Resource.Preparations.FilterArchived',
                        'Elixir.AshArchival.Resource.Transformers.SetupArchival']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,ash]},
              {description,"An Ash extension to implement archival (soft deletion) for resources.\n"},
              {registered,[]},
              {vsn,"1.1.2"}]}.
