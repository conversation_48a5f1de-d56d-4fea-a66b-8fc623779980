{application,ash_paper_trail,
             [{modules,['Elixir.AshPaperTrail',
                        'Elixir.AshPaperTrail.ChangeBuilders.ChangesOnly',
                        'Elixir.AshPaperTrail.ChangeBuilders.FullDiff',
                        'Elixir.AshPaperTrail.ChangeBuilders.FullDiff.EmbeddedChange',
                        'Elixir.AshPaperTrail.ChangeBuilders.FullDiff.Helpers',
                        'Elixir.AshPaperTrail.ChangeBuilders.FullDiff.ListChange',
                        'Elixir.AshPaperTrail.ChangeBuilders.FullDiff.SimpleChange',
                        'Elixir.AshPaperTrail.ChangeBuilders.FullDiff.UnionChange',
                        'Elixir.AshPaperTrail.ChangeBuilders.Snapshot',
                        'Elixir.AshPaperTrail.Domain',
                        'Elixir.AshPaperTrail.Domain.Info',
                        'Elixir.AshPaperTrail.Domain.PaperTrail.Options',
                        'Elixir.AshPaperTrail.Domain.Transformers.AllowResourceVersions',
                        'Elixir.AshPaperTrail.Resource',
                        'Elixir.AshPaperTrail.Resource.BelongsToActor',
                        'Elixir.AshPaperTrail.Resource.Changes.CreateNewVersion',
                        'Elixir.AshPaperTrail.Resource.Info',
                        'Elixir.AshPaperTrail.Resource.PaperTrail.BelongsToActor',
                        'Elixir.AshPaperTrail.Resource.PaperTrail.BelongsToActor.Options',
                        'Elixir.AshPaperTrail.Resource.PaperTrail.Options',
                        'Elixir.AshPaperTrail.Resource.Transformers.CreateVersionResource',
                        'Elixir.AshPaperTrail.Resource.Transformers.RelateVersionResource',
                        'Elixir.AshPaperTrail.Resource.Transformers.ValidateBelongsToActor',
                        'Elixir.AshPaperTrail.Resource.Transformers.VersionOnChange']},
              {compile_env,[{ash,[default_belongs_to_type],error}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,ash]},
              {description,"The extension for keeping an audit log of changes to your Ash resources.\n"},
              {registered,[]},
              {vsn,"0.5.6"}]}.
