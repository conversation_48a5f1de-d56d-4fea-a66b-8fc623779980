{application,ash_phoenix,
             [{modules,['Elixir.AshPhoenix','Elixir.AshPhoenix.FilterForm',
                        'Elixir.AshPhoenix.FilterForm.Arguments',
                        'Elixir.AshPhoenix.FilterForm.Predicate',
                        'Elixir.AshPhoenix.Form',
                        'Elixir.AshPhoenix.Form.Auto',
                        'Elixir.AshPhoenix.Form.InvalidPath',
                        'Elixir.AshPhoenix.Form.NoActionConfigured',
                        'Elixir.AshPhoenix.Form.NoDataLoaded',
                        'Elixir.AshPhoenix.Form.NoFormConfigured',
                        'Elixir.AshPhoenix.Form.NoResourceConfigured',
                        'Elixir.AshPhoenix.Form.WrappedValue',
                        'Elixir.AshPhoenix.Form.WrappedValue.EctoType',
                        'Elixir.AshPhoenix.FormData.Error',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Action.InvalidArgument',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Changes.InvalidArgument',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Changes.InvalidAttribute',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Changes.InvalidChanges',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Changes.InvalidRelationship',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Changes.Required',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Invalid.NoSuchInput',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Query.InvalidArgument',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Query.InvalidCalculationArgument',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Query.InvalidQuery',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Query.NotFound',
                        'Elixir.AshPhoenix.FormData.Error.Ash.Error.Query.Required',
                        'Elixir.AshPhoenix.FormData.Helpers',
                        'Elixir.AshPhoenix.FormDefinition',
                        'Elixir.AshPhoenix.Forms.Form',
                        'Elixir.AshPhoenix.Forms.Form.Options',
                        'Elixir.AshPhoenix.Gen','Elixir.AshPhoenix.Helpers',
                        'Elixir.AshPhoenix.Info','Elixir.AshPhoenix.LiveView',
                        'Elixir.AshPhoenix.LiveView.AssignPageAndStreamResultOptions',
                        'Elixir.AshPhoenix.LiveView.SubdomainHook',
                        'Elixir.AshPhoenix.Plug.CheckCodegenStatus',
                        'Elixir.AshPhoenix.SubdomainPlug',
                        'Elixir.AshPhoenix.Transformers.AddFormCodeInterfaces',
                        'Elixir.AshPhoenix.Verifiers.VerifyFormDefinitions',
                        'Elixir.Inspect.AshPhoenix.Form',
                        'Elixir.Inspect.AshPhoenix.Form.WrappedValue',
                        'Elixir.Mix.Tasks.AshPhoenix.Gen.Html',
                        'Elixir.Mix.Tasks.AshPhoenix.Gen.Live',
                        'Elixir.Mix.Tasks.AshPhoenix.Install',
                        'Elixir.Phoenix.HTML.FormData.AshPhoenix.FilterForm',
                        'Elixir.Phoenix.HTML.FormData.AshPhoenix.FilterForm.Arguments',
                        'Elixir.Phoenix.HTML.FormData.AshPhoenix.FilterForm.Predicate',
                        'Elixir.Phoenix.HTML.FormData.AshPhoenix.Form',
                        'Elixir.Phoenix.HTML.Safe.Ash.CiString',
                        'Elixir.Phoenix.HTML.Safe.Ash.NotLoaded',
                        'Elixir.Phoenix.Param.Ash.CiString',
                        'Elixir.Phoenix.Param.Ash.NotLoaded',
                        'Elixir.Plug.Exception.Ash.Error.Forbidden',
                        'Elixir.Plug.Exception.Ash.Error.Forbidden.DomainRequiresActor',
                        'Elixir.Plug.Exception.Ash.Error.Forbidden.MustPassStrictCheck',
                        'Elixir.Plug.Exception.Ash.Error.Forbidden.Policy',
                        'Elixir.Plug.Exception.Ash.Error.Framework',
                        'Elixir.Plug.Exception.Ash.Error.Framework.PendingCodegen',
                        'Elixir.Plug.Exception.Ash.Error.Invalid',
                        'Elixir.Plug.Exception.Ash.Error.Invalid.InvalidPrimaryKey',
                        'Elixir.Plug.Exception.Ash.Error.Query.InvalidArgument',
                        'Elixir.Plug.Exception.Ash.Error.Query.InvalidCalculationArgument',
                        'Elixir.Plug.Exception.Ash.Error.Query.InvalidFilterValue',
                        'Elixir.Plug.Exception.Ash.Error.Query.NotFound',
                        'Elixir.Plug.Exception.Ash.Error.Unknown']},
              {compile_env,[{ash,['include_embedded_source_by_default?'],
                                 error}]},
              {optional_applications,[inertia,igniter]},
              {applications,[kernel,stdlib,elixir,logger,ash,phoenix,
                             phoenix_html,phoenix_live_view,spark,inertia,
                             igniter]},
              {description,"Utilities for integrating Ash and Phoenix\n"},
              {registered,[]},
              {vsn,"2.3.9"}]}.
