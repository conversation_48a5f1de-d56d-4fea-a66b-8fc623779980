{application,ash_postgres,
             [{modules,['Elixir.AshPostgres',
                        'Elixir.AshPostgres.CheckConstraint',
                        'Elixir.AshPostgres.CustomAggregate',
                        'Elixir.AshPostgres.CustomExtension',
                        'Elixir.AshPostgres.CustomIndex',
                        'Elixir.AshPostgres.DataLayer',
                        'Elixir.AshPostgres.DataLayer.CheckConstraints',
                        'Elixir.AshPostgres.DataLayer.CustomIndexes',
                        'Elixir.AshPostgres.DataLayer.CustomStatements',
                        'Elixir.AshPostgres.DataLayer.Info',
                        'Elixir.AshPostgres.DataLayer.ManageTenant',
                        'Elixir.AshPostgres.DataLayer.ManageTenant.Options',
                        'Elixir.AshPostgres.DataLayer.Postgres.CheckConstraints.CheckConstraint',
                        'Elixir.AshPostgres.DataLayer.Postgres.CheckConstraints.CheckConstraint.Options',
                        'Elixir.AshPostgres.DataLayer.Postgres.CustomIndexes.Index',
                        'Elixir.AshPostgres.DataLayer.Postgres.CustomIndexes.Index.Options',
                        'Elixir.AshPostgres.DataLayer.Postgres.CustomStatements.Statement',
                        'Elixir.AshPostgres.DataLayer.Postgres.CustomStatements.Statement.Options',
                        'Elixir.AshPostgres.DataLayer.Postgres.Options',
                        'Elixir.AshPostgres.DataLayer.Postgres.References.Reference',
                        'Elixir.AshPostgres.DataLayer.Postgres.References.Reference.Options',
                        'Elixir.AshPostgres.DataLayer.References',
                        'Elixir.AshPostgres.DataLayer.References.Options',
                        'Elixir.AshPostgres.Extensions.Vector',
                        'Elixir.AshPostgres.Functions.Binding',
                        'Elixir.AshPostgres.Functions.ILike',
                        'Elixir.AshPostgres.Functions.Like',
                        'Elixir.AshPostgres.Functions.TrigramSimilarity',
                        'Elixir.AshPostgres.Functions.VectorCosineDistance',
                        'Elixir.AshPostgres.Functions.VectorL2Distance',
                        'Elixir.AshPostgres.Ltree',
                        'Elixir.AshPostgres.Ltree.EctoType',
                        'Elixir.AshPostgres.ManualRelationship',
                        'Elixir.AshPostgres.Migration',
                        'Elixir.AshPostgres.MigrationCompileCache',
                        'Elixir.AshPostgres.MigrationGenerator',
                        'Elixir.AshPostgres.MigrationGenerator.AshFunctions',
                        'Elixir.AshPostgres.MigrationGenerator.Operation',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddAttribute',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddCheckConstraint',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddCustomIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddCustomStatement',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddPrimaryKey',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddPrimaryKeyDown',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddReferenceIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AddUniqueIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AlterAttribute',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.AlterDeferrability',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.CreateTable',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.DropForeignKey',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.Helper',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemoveAttribute',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemoveCheckConstraint',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemoveCustomIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemoveCustomStatement',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemovePrimaryKey',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemovePrimaryKeyDown',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemoveReferenceIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RemoveUniqueIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RenameAttribute',
                        'Elixir.AshPostgres.MigrationGenerator.Operation.RenameUniqueIndex',
                        'Elixir.AshPostgres.MigrationGenerator.Phase',
                        'Elixir.AshPostgres.MigrationGenerator.Phase.Alter',
                        'Elixir.AshPostgres.MigrationGenerator.Phase.Create',
                        'Elixir.AshPostgres.Mix.Helpers',
                        'Elixir.AshPostgres.MultiTenancy',
                        'Elixir.AshPostgres.Reference',
                        'Elixir.AshPostgres.Repo',
                        'Elixir.AshPostgres.Repo.BeforeCompile',
                        'Elixir.AshPostgres.ResourceGenerator.SensitiveData',
                        'Elixir.AshPostgres.ResourceGenerator.Spec',
                        'Elixir.AshPostgres.ResourceGenerator.Spec.Attribute',
                        'Elixir.AshPostgres.ResourceGenerator.Spec.CheckConstraint',
                        'Elixir.AshPostgres.ResourceGenerator.Spec.ForeignKey',
                        'Elixir.AshPostgres.ResourceGenerator.Spec.Index',
                        'Elixir.AshPostgres.ResourceGenerator.Spec.Relationship',
                        'Elixir.AshPostgres.SqlImplementation',
                        'Elixir.AshPostgres.Statement',
                        'Elixir.AshPostgres.Timestamptz',
                        'Elixir.AshPostgres.Timestamptz.EctoType',
                        'Elixir.AshPostgres.TimestamptzUsec',
                        'Elixir.AshPostgres.TimestamptzUsec.EctoType',
                        'Elixir.AshPostgres.Tsquery',
                        'Elixir.AshPostgres.Tsquery.EctoType',
                        'Elixir.AshPostgres.Tsvector',
                        'Elixir.AshPostgres.Tsvector.EctoType',
                        'Elixir.AshPostgres.Type',
                        'Elixir.AshPostgres.Type.CiStringWrapper',
                        'Elixir.AshPostgres.Type.CiStringWrapper.EctoType',
                        'Elixir.AshPostgres.Type.StringWrapper',
                        'Elixir.AshPostgres.Type.StringWrapper.EctoType',
                        'Elixir.AshPostgres.Verifiers.EnsureTableOrPolymorphic',
                        'Elixir.AshPostgres.Verifiers.PreventAttributeMultitenancyAndNonFullMatchType',
                        'Elixir.AshPostgres.Verifiers.PreventMultidimensionalArrayAggregates',
                        'Elixir.AshPostgres.Verifiers.ValidateIdentityIndexNames',
                        'Elixir.AshPostgres.Verifiers.ValidateReferences',
                        'Elixir.EctoMigrationDefault',
                        'Elixir.EctoMigrationDefault.Any',
                        'Elixir.EctoMigrationDefault.Atom',
                        'Elixir.EctoMigrationDefault.BitString',
                        'Elixir.EctoMigrationDefault.Date',
                        'Elixir.EctoMigrationDefault.DateTime',
                        'Elixir.EctoMigrationDefault.Decimal',
                        'Elixir.EctoMigrationDefault.Float',
                        'Elixir.EctoMigrationDefault.Integer',
                        'Elixir.EctoMigrationDefault.NaiveDateTime',
                        'Elixir.EctoMigrationDefault.Time',
                        'Elixir.Inspect.AshPostgres.Functions.Binding',
                        'Elixir.Inspect.AshPostgres.Functions.ILike',
                        'Elixir.Inspect.AshPostgres.Functions.Like',
                        'Elixir.Inspect.AshPostgres.Functions.TrigramSimilarity',
                        'Elixir.Inspect.AshPostgres.Functions.VectorCosineDistance',
                        'Elixir.Inspect.AshPostgres.Functions.VectorL2Distance',
                        'Elixir.Mix.Tasks.AshPostgres.Create',
                        'Elixir.Mix.Tasks.AshPostgres.Drop',
                        'Elixir.Mix.Tasks.AshPostgres.Gen.Resources',
                        'Elixir.Mix.Tasks.AshPostgres.GenerateMigrations',
                        'Elixir.Mix.Tasks.AshPostgres.Install',
                        'Elixir.Mix.Tasks.AshPostgres.Migrate',
                        'Elixir.Mix.Tasks.AshPostgres.Rollback',
                        'Elixir.Mix.Tasks.AshPostgres.SetupVector',
                        'Elixir.Mix.Tasks.AshPostgres.SetupVector.Docs',
                        'Elixir.Mix.Tasks.AshPostgres.SquashSnapshots']},
              {optional_applications,[igniter]},
              {applications,[kernel,stdlib,elixir,ash,ash_sql,igniter,
                             ecto_sql,ecto,jason,postgrex]},
              {description,"The PostgreSQL data layer for Ash Framework\n"},
              {registered,[]},
              {vsn,"2.6.9"}]}.
