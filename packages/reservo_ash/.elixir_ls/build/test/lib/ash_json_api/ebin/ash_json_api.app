{application,ash_json_api,
    [{modules,
         ['Elixir.AshJsonApi','Elixir.AshJsonApi.Controllers.Delete',
          'Elixir.AshJsonApi.Controllers.DeleteFromRelationship',
          'Elixir.AshJsonApi.Controllers.GenericActionRoute',
          'Elixir.AshJsonApi.Controllers.Get',
          'Elixir.AshJsonApi.Controllers.GetRelated',
          'Elixir.AshJsonApi.Controllers.GetRelationship',
          'Elixir.AshJsonApi.Controllers.Helpers',
          'Elixir.AshJsonApi.Controllers.Index',
          'Elixir.AshJsonApi.Controllers.NoRouteFound',
          'Elixir.AshJsonApi.Controllers.OpenApi',
          'Elixir.AshJsonApi.Controllers.Patch',
          'Elixir.AshJsonApi.Controllers.PatchRelationship',
          'Elixir.AshJsonApi.Controllers.Post',
          'Elixir.AshJsonApi.Controllers.PostToRelationship',
          'Elixir.AshJsonApi.Controllers.Response',
          'Elixir.AshJsonApi.Controllers.Router',
          'Elixir.AshJsonApi.Controllers.Schema','Elixir.AshJsonApi.Domain',
          'Elixir.AshJsonApi.Domain.BaseRoute',
          'Elixir.AshJsonApi.Domain.Info',
          'Elixir.AshJsonApi.Domain.JsonApi.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Delete',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Delete.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.DeleteFromRelationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.DeleteFromRelationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Get',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Get.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Index',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Index.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Patch',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Patch.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.PatchRelationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.PatchRelationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Post',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Post.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.PostToRelationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.PostToRelationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Related',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Related.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Relationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Relationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Route',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.BaseRoute.Route.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Delete',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Delete.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.DeleteFromRelationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.DeleteFromRelationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Get',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Get.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Index',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Index.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Patch',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Patch.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.PatchRelationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.PatchRelationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Post',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Post.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.PostToRelationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.PostToRelationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Related',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Related.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Relationship',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Relationship.Options',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Route',
          'Elixir.AshJsonApi.Domain.JsonApi.Routes.Route.Options',
          'Elixir.AshJsonApi.Domain.OpenApi',
          'Elixir.AshJsonApi.Domain.OpenApi.Options',
          'Elixir.AshJsonApi.Domain.Persisters.DefineRouter',
          'Elixir.AshJsonApi.Domain.Routes',
          'Elixir.AshJsonApi.Domain.Transformers.SetBaseRoutes',
          'Elixir.AshJsonApi.Domain.Verifiers.VerifyActions',
          'Elixir.AshJsonApi.Domain.Verifiers.VerifyHasType',
          'Elixir.AshJsonApi.Domain.Verifiers.VerifyOpenApiGrouping',
          'Elixir.AshJsonApi.Domain.Verifiers.VerifyQueryParams',
          'Elixir.AshJsonApi.Domain.Verifiers.VerifyRelationships',
          'Elixir.AshJsonApi.Error','Elixir.AshJsonApi.Error.InvalidBody',
          'Elixir.AshJsonApi.Error.InvalidField',
          'Elixir.AshJsonApi.Error.InvalidHeader',
          'Elixir.AshJsonApi.Error.InvalidIncludes',
          'Elixir.AshJsonApi.Error.InvalidPagination',
          'Elixir.AshJsonApi.Error.InvalidQuery',
          'Elixir.AshJsonApi.Error.InvalidRelationshipInput',
          'Elixir.AshJsonApi.Error.InvalidType',
          'Elixir.AshJsonApi.Error.NotFound',
          'Elixir.AshJsonApi.Error.SchemaErrors',
          'Elixir.AshJsonApi.Error.UnacceptableMediaType',
          'Elixir.AshJsonApi.Error.UnsupportedMediaType',
          'Elixir.AshJsonApi.Includes.Includer',
          'Elixir.AshJsonApi.Includes.Parser','Elixir.AshJsonApi.JsonSchema',
          'Elixir.AshJsonApi.OpenApi','Elixir.AshJsonApi.OpenApiSpexChecker',
          'Elixir.AshJsonApi.Plug.Parser','Elixir.AshJsonApi.Request',
          'Elixir.AshJsonApi.Resource','Elixir.AshJsonApi.Resource.Info',
          'Elixir.AshJsonApi.Resource.JsonApi.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Delete',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Delete.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.DeleteFromRelationship',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.DeleteFromRelationship.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Get',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Get.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Index',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Index.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Patch',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Patch.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.PatchRelationship',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.PatchRelationship.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Post',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Post.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.PostToRelationship',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.PostToRelationship.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Related',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Related.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Relationship',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Relationship.Options',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Route',
          'Elixir.AshJsonApi.Resource.JsonApi.Routes.Route.Options',
          'Elixir.AshJsonApi.Resource.Persisters.DefineRouter',
          'Elixir.AshJsonApi.Resource.PrimaryKey',
          'Elixir.AshJsonApi.Resource.PrimaryKey.Options',
          'Elixir.AshJsonApi.Resource.Route',
          'Elixir.AshJsonApi.Resource.Routes',
          'Elixir.AshJsonApi.Resource.Routes.Options',
          'Elixir.AshJsonApi.Resource.Transformers.PrependRoutePrefix',
          'Elixir.AshJsonApi.Resource.Transformers.RequirePrimaryKey',
          'Elixir.AshJsonApi.Resource.Transformers.ValidateNoOverlappingRoutes',
          'Elixir.AshJsonApi.Resource.Verifiers.VerifyActions',
          'Elixir.AshJsonApi.Resource.Verifiers.VerifyHasType',
          'Elixir.AshJsonApi.Resource.Verifiers.VerifyIncludes',
          'Elixir.AshJsonApi.Resource.Verifiers.VerifyQueryParams',
          'Elixir.AshJsonApi.Resource.Verifiers.VerifyRelationships',
          'Elixir.AshJsonApi.Router',
          'Elixir.AshJsonApi.Router.ConsoleFormatter',
          'Elixir.AshJsonApi.Serializer','Elixir.AshJsonApi.Test',
          'Elixir.AshJsonApi.ToJsonApiError',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Action.InvalidArgument',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Changes.InvalidArgument',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Changes.InvalidAttribute',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Changes.InvalidChanges',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Changes.Required',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Forbidden.ForbiddenField',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Forbidden.Policy',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Invalid.InvalidPrimaryKey',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Invalid.NoSuchInput',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Page.InvalidKeyset',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Query.InvalidArgument',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Query.InvalidQuery',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Query.NotFound',
          'Elixir.AshJsonApi.ToJsonApiError.Ash.Error.Query.Required',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidBody',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidField',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidHeader',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidIncludes',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidPagination',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidQuery',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidRelationshipInput',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.InvalidType',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.NotFound',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.UnacceptableMediaType',
          'Elixir.AshJsonApi.ToJsonApiError.AshJsonApi.Error.UnsupportedMediaType',
          'Elixir.AshJsonApi.Type','Elixir.Mix.Tasks.AshJsonApi.Install',
          'Elixir.Mix.Tasks.AshJsonApi.Routes']},
     {compile_env,
         [{ash_json_api,['authorize_update_destroy_with_error?'],error},
          {ash_json_api,['show_public_calculations_when_loaded?'],error}]},
     {optional_applications,[igniter,open_api_spex]},
     {applications,
         [kernel,stdlib,elixir,logger,ash,spark,igniter,plug,jason,phoenix,
          json_xema,open_api_spex]},
     {description,"The JSON:API extension for the Ash Framework.\n"},
     {registered,[]},
     {vsn,"1.4.36"}]}.
