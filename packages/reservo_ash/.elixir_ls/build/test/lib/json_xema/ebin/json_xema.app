{application,json_xema,
             [{modules,['Elixir.JsonXema','Elixir.JsonXema.SchemaError',
                        'Elixir.JsonXema.SchemaValidator',
                        'Elixir.JsonXema.ValidationError']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,xema,conv_case]},
              {description,"A JSON Schema validator for draft-04, -06, and -07."},
              {registered,[]},
              {vsn,"0.6.5"}]}.
