{application,open_api_spex,
             [{modules,['Elixir.Inspect.OpenApiSpex.Schema',
                        'Elixir.Jason.Encoder.OpenApiSpex.JsonErrorResponse',
                        'Elixir.Jason.Encoder.OpenApiSpex.OpenApi',
                        'Elixir.Mix.Tasks.Openapi.Spec.Json',
                        'Elixir.Mix.Tasks.Openapi.Spec.Yaml',
                        'Elixir.OpenApiSpex','Elixir.OpenApiSpex.Callback',
                        'Elixir.OpenApiSpex.Cast',
                        'Elixir.OpenApiSpex.Cast.AllOf',
                        'Elixir.OpenApiSpex.Cast.AnyOf',
                        'Elixir.OpenApiSpex.Cast.Array',
                        'Elixir.OpenApiSpex.Cast.Discriminator',
                        'Elixir.OpenApiSpex.Cast.Enum',
                        'Elixir.OpenApiSpex.Cast.Error',
                        'Elixir.OpenApiSpex.Cast.Integer',
                        'Elixir.OpenApiSpex.Cast.Number',
                        'Elixir.OpenApiSpex.Cast.Object',
                        'Elixir.OpenApiSpex.Cast.OneOf',
                        'Elixir.OpenApiSpex.Cast.Primitive',
                        'Elixir.OpenApiSpex.Cast.String',
                        'Elixir.OpenApiSpex.Cast.Utils',
                        'Elixir.OpenApiSpex.CastParameters',
                        'Elixir.OpenApiSpex.Components',
                        'Elixir.OpenApiSpex.Contact',
                        'Elixir.OpenApiSpex.Controller',
                        'Elixir.OpenApiSpex.ControllerSpecs',
                        'Elixir.OpenApiSpex.DeprecatedCast',
                        'Elixir.OpenApiSpex.Discriminator',
                        'Elixir.OpenApiSpex.Encoding',
                        'Elixir.OpenApiSpex.Example',
                        'Elixir.OpenApiSpex.ExportSpec',
                        'Elixir.OpenApiSpex.ExportSpec.Options',
                        'Elixir.OpenApiSpex.Extendable',
                        'Elixir.OpenApiSpex.Extendable.Any',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Components',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Contact',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Discriminator',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Encoding',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Example',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.ExternalDocumentation',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Header',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Info',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.License',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Link',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.MediaType',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.OAuthFlow',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.OAuthFlows',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.OpenApi',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Operation',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Parameter',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.PathItem',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.RequestBody',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Response',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Schema',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.SecurityScheme',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Server',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.ServerVariable',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Tag',
                        'Elixir.OpenApiSpex.Extendable.OpenApiSpex.Xml',
                        'Elixir.OpenApiSpex.ExternalDocumentation',
                        'Elixir.OpenApiSpex.Header','Elixir.OpenApiSpex.Info',
                        'Elixir.OpenApiSpex.JsonErrorResponse',
                        'Elixir.OpenApiSpex.License',
                        'Elixir.OpenApiSpex.Link',
                        'Elixir.OpenApiSpex.MediaType',
                        'Elixir.OpenApiSpex.OAuthFlow',
                        'Elixir.OpenApiSpex.OAuthFlows',
                        'Elixir.OpenApiSpex.OpenApi',
                        'Elixir.OpenApiSpex.OpenApi.Decode',
                        'Elixir.OpenApiSpex.OpenApi.YmlrEncoder',
                        'Elixir.OpenApiSpex.Operation',
                        'Elixir.OpenApiSpex.Operation2',
                        'Elixir.OpenApiSpex.OperationBuilder',
                        'Elixir.OpenApiSpex.Parameter',
                        'Elixir.OpenApiSpex.PathItem',
                        'Elixir.OpenApiSpex.Paths',
                        'Elixir.OpenApiSpex.Plug.AppEnvCache',
                        'Elixir.OpenApiSpex.Plug.Cache',
                        'Elixir.OpenApiSpex.Plug.Cast',
                        'Elixir.OpenApiSpex.Plug.Cast2',
                        'Elixir.OpenApiSpex.Plug.CastAndValidate',
                        'Elixir.OpenApiSpex.Plug.DefaultRenderError',
                        'Elixir.OpenApiSpex.Plug.JsonRenderError',
                        'Elixir.OpenApiSpex.Plug.JsonRenderErrorV2',
                        'Elixir.OpenApiSpex.Plug.NoneCache',
                        'Elixir.OpenApiSpex.Plug.PersistentTermCache',
                        'Elixir.OpenApiSpex.Plug.PutApiSpec',
                        'Elixir.OpenApiSpex.Plug.RenderSpec',
                        'Elixir.OpenApiSpex.Plug.SwaggerUI',
                        'Elixir.OpenApiSpex.Plug.SwaggerUIOAuth2Redirect',
                        'Elixir.OpenApiSpex.Plug.Validate',
                        'Elixir.OpenApiSpex.Reference',
                        'Elixir.OpenApiSpex.RequestBody',
                        'Elixir.OpenApiSpex.Response',
                        'Elixir.OpenApiSpex.Responses',
                        'Elixir.OpenApiSpex.Schema',
                        'Elixir.OpenApiSpex.SchemaConsistency',
                        'Elixir.OpenApiSpex.SchemaException',
                        'Elixir.OpenApiSpex.SchemaResolver',
                        'Elixir.OpenApiSpex.SecurityRequirement',
                        'Elixir.OpenApiSpex.SecurityScheme',
                        'Elixir.OpenApiSpex.Server',
                        'Elixir.OpenApiSpex.ServerVariable',
                        'Elixir.OpenApiSpex.Tag',
                        'Elixir.OpenApiSpex.TermType',
                        'Elixir.OpenApiSpex.Test.Assertions',
                        'Elixir.OpenApiSpex.Test.Assertions2',
                        'Elixir.OpenApiSpex.TestAssertions',
                        'Elixir.OpenApiSpex.Xml',
                        'Elixir.String.Chars.OpenApiSpex.Cast.Error']},
              {optional_applications,[jason,decimal,poison,ymlr]},
              {applications,[kernel,stdlib,elixir,jason,decimal,plug,poison,
                             ymlr]},
              {description,"open_api_spex"},
              {registered,[]},
              {vsn,"3.21.3"}]}.
