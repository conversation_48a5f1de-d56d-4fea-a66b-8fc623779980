{application,mime,
             [{modules,['Elixir.MIME']},
              {compile_env,[{mime,[extensions],
                                  {ok,#{<<"json">> =>
                                            <<"application/vnd.api+json">>}}},
                            {mime,[suffixes],error},
                            {mime,[types],
                                  {ok,#{<<"application/vnd.api+json">> =>
                                            [<<"json">>]}}}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"A MIME type module for Elixir"},
              {registered,[]},
              {vsn,"2.0.7"},
              {env,[]}]}.
