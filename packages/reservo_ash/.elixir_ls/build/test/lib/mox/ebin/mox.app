{application,mox,
             [{modules,['Elixir.Mox','Elixir.Mox.Application',
                        'Elixir.Mox.UnexpectedCallError',
                        'Elixir.Mox.VerificationError']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,nimble_ownership]},
              {description,"Mocks and explicit contracts for Elixir"},
              {registered,[]},
              {vsn,"1.2.0"},
              {mod,{'Elixir.Mox.Application',[]}}]}.
